# Python 3.11.9 Compatible Requirements
# Optimized for Python 3.11.9 with resolved compatibility conflicts

# Essential build tools
setuptools>=68.0.0,<76.0.0
wheel>=0.41.0,<0.45.0

# Core numerical computing
numpy>=1.24.0,<2.1.0

# PyTorch ecosystem
torch>=2.1.0,<2.6.0
torchvision>=0.16.0,<0.21.0
torchaudio>=2.1.0,<2.6.0

# Computer Vision and Image Processing
opencv-python>=4.8.0,<4.11.0
Pillow>=6.2.0,<10.0.0
albumentations>=1.3.1,<1.5.0


# Data Science Core
pandas>=2.0.0,<2.3.0
scikit-learn>=1.3.0,<1.6.0

# Visualization
matplotlib>=3.7.0,<3.9.0
seaborn>=0.12.0,<0.14.0
plotly>=5.17.0,<5.25.0

# Progress and Monitoring
tqdm>=4.66.0,<5.0.0
tensorboard>=2.15.0,<2.18.0

# Jupyter Environment
jupyter>=1.0.0,<2.0.0
notebook>=7.0.0,<7.3.0
jupyterlab>=4.0.0,<4.3.0
ipywidgets>=8.1.0,<8.3.0
ipython>=8.18.0,<8.30.0

# Configuration and Utilities
PyYAML>=6.0.1,<6.1.0
requests>=2.31.0,<2.33.0
lxml>=4.9.3,<5.4.0
xmltodict>=0.13.0,<0.14.0

# Object Detection Specific
pycocotools>=2.0.7,<2.1.0

# ONNX & Runtime (CPU support on all platforms)
onnx>=1.15.0,<1.18.0
onnxruntime>=1.16.0,<1.20.0

# Dataset Management (optional)
kaggle>=1.6.0,<1.7.0
kagglehub>=0.2.0,<0.4.0

# Web/GUI Frameworks (optional)
gradio>=3.30.0,<3.40.0
streamlit>=1.24.0,<1.26.0
flask>=2.3.0,<2.4.0

# Async libraries (optional)
# aiohttp>=3.8.0,<4.0.0
# fastapi>=0.95.0,<0.101.0

# Python 3.11.9 specific optimizations
typing-extensions>=4.8.0,<5.0.0
