{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# SSD (Single Shot MultiBox Detector) 网络教学教程\n",
    "\n",
    "[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/your-username/ssd-tutorial/HEAD)\n",
    "\n",
    "## 概述\n",
    "这个综合教程将教您如何使用PyTorch实现和训练SSD（Single Shot MultiBox Detector）网络进行植物病害检测。您将学习目标检测基础知识、数据预处理、模型架构、训练策略和部署技术。\n",
    "\n",
    "### 您将学到的内容：\n",
    "- SSD架构和多尺度特征检测\n",
    "- 目标检测数据预处理和数据增强\n",
    "- 使用预训练骨干网络的迁移学习\n",
    "- 训练监控和评估指标\n",
    "- 模型部署和推理优化\n",
    "\n",
    "### 数据集：\n",
    "我们将使用来自Kaggle的植物病害检测数据集，该数据集包含各种植物病害的图像和边界框标注。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 环境设置\n",
    "\n",
    "让我们首先设置环境并检查所有依赖项。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 294,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "所有依赖项安装成功！\n"
     ]
    }
   ],
   "source": [
    "# 如果在Colab或新环境中运行，安装依赖项\n",
    "import sys\n",
    "import subprocess\n",
    "\n",
    "def install_requirements():\n",
    "    \"\"\"从requirements.txt安装所需的包\"\"\"\n",
    "    try:\n",
    "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \"-r\", \"requirements.txt\"])\n",
    "        print(\"所有依赖项安装成功！\")\n",
    "    except subprocess.CalledProcessError as e:\n",
    "        print(f\"安装依赖项时出错：{e}\")\n",
    "        print(\"请手动安装：pip install -r requirements.txt\")\n",
    "\n",
    "# 如果需要安装依赖项，请取消注释下面的行\n",
    "install_requirements()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 295,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "基础库导入成功！\n"
     ]
    }
   ],
   "source": [
    "# 导入基础库\n",
    "import torch\n",
    "import torchvision\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import cv2\n",
    "import os\n",
    "import sys\n",
    "import platform\n",
    "from pathlib import Path\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# 设置更好的绘图样式\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"基础库导入成功！\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 296,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "环境信息：\n",
      "==================================================\n",
      "平台：Windows-10-10.0.19045-SP0\n",
      "Python版本：3.11.9\n",
      "PyTorch版本：2.5.1+cpu\n",
      "TorchVision版本：0.20.1+cpu\n",
      "CUDA可用：否（将使用CPU）\n",
      "选择的设备：cpu\n"
     ]
    }
   ],
   "source": [
    "# 环境和版本检查\n",
    "def check_environment():\n",
    "    \"\"\"检查并显示环境信息\"\"\"\n",
    "    print(\"环境信息：\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    # 系统信息\n",
    "    print(f\"平台：{platform.platform()}\")\n",
    "    print(f\"Python版本：{sys.version.split()[0]}\")\n",
    "    \n",
    "    # PyTorch信息\n",
    "    print(f\"PyTorch版本：{torch.__version__}\")\n",
    "    print(f\"TorchVision版本：{torchvision.__version__}\")\n",
    "    \n",
    "    # CUDA信息\n",
    "    if torch.cuda.is_available():\n",
    "        print(f\"CUDA可用：是\")\n",
    "        print(f\"CUDA版本：{torch.version.cuda}\")\n",
    "        print(f\"GPU数量：{torch.cuda.device_count()}\")\n",
    "        for i in range(torch.cuda.device_count()):\n",
    "            print(f\"   GPU {i}：{torch.cuda.get_device_name(i)}\")\n",
    "    else:\n",
    "        print(f\"CUDA可用：否（将使用CPU）\")\n",
    "    \n",
    "    # 设置设备\n",
    "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "    print(f\"选择的设备：{device}\")\n",
    "    \n",
    "    return device\n",
    "\n",
    "device = check_environment()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 298,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "配置加载成功！\n",
      "工作目录已创建：['models_dir', 'logs_dir', 'image_size']\n",
      "随机种子设置为：42\n"
     ]
    }
   ],
   "source": [
    "# 集中配置\n",
    "CONFIG = {\n",
    "    # 设备和性能\n",
    "    'device': device,\n",
    "    'num_workers': 4,\n",
    "    'pin_memory': True,\n",
    "    \n",
    "    # 数据路径\n",
    "    'data_root': './data',\n",
    "    'dataset_name': 'plant-diseases-detection-dataset',\n",
    "    'images_dir': './data/images',\n",
    "    'annotations_dir': './data/annotations',\n",
    "    'models_dir': './models',\n",
    "    'logs_dir': './logs',\n",
    "    \n",
    "    # 图像处理\n",
    "    'image_size': (300, 300),  # SSD300输入尺寸\n",
    "    'mean': [0.485, 0.456, 0.406],  # ImageNet标准化\n",
    "    'std': [0.229, 0.224, 0.225],\n",
    "    \n",
    "    # 模型架构\n",
    "    'backbone': 'vgg16',\n",
    "    'pretrained': True,\n",
    "    'num_classes': None,  # 将在数据集分析后设置\n",
    "    \n",
    "    # 训练超参数\n",
    "    'batch_size': 16,\n",
    "    'learning_rate': 1e-3,\n",
    "    'weight_decay': 5e-4,\n",
    "    'momentum': 0.9,\n",
    "    'epochs': 50,\n",
    "    'warmup_epochs': 5,\n",
    "    \n",
    "    # 损失函数\n",
    "    'neg_pos_ratio': 3,  # 困难负样本挖掘比例\n",
    "    'alpha': 1.0,  # 定位损失权重\n",
    "    \n",
    "    # 评估\n",
    "    'confidence_threshold': 0.5,\n",
    "    'nms_threshold': 0.5,\n",
    "    'iou_threshold': 0.5,\n",
    "    \n",
    "    # 数据增强\n",
    "    'augmentation_prob': 0.5,\n",
    "    'horizontal_flip_prob': 0.5,\n",
    "    'brightness_limit': 0.2,\n",
    "    'contrast_limit': 0.2,\n",
    "    \n",
    "    # 监控\n",
    "    'save_every': 10,  # 每N个epoch保存模型\n",
    "    'log_every': 100,  # 每N个batch记录指标\n",
    "    'tensorboard_log': True,\n",
    "    \n",
    "    # 随机种子\n",
    "    'seed': 42\n",
    "}\n",
    "\n",
    "# 创建必要的目录\n",
    "for dir_path in [CONFIG['data_root'], CONFIG['models_dir'], CONFIG['logs_dir']]:\n",
    "    Path(dir_path).mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "# 设置随机种子以确保可重现性\n",
    "torch.manual_seed(CONFIG['seed'])\n",
    "np.random.seed(CONFIG['seed'])\n",
    "if torch.cuda.is_available():\n",
    "    torch.cuda.manual_seed(CONFIG['seed'])\n",
    "    torch.cuda.manual_seed_all(CONFIG['seed'])\n",
    "\n",
    "print(\"配置加载成功！\")\n",
    "print(f\"工作目录已创建：{list(CONFIG.keys())[7:10]}\")\n",
    "print(f\"随机种子设置为：{CONFIG['seed']}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 299,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "正在验证配置...\n",
      "配置验证通过！\n"
     ]
    },
    {
     "data": {
      "text/plain": [
       "True"
      ]
     },
     "execution_count": 299,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 配置验证\n",
    "def validate_config(config):\n",
    "    \"\"\"验证配置参数\"\"\"\n",
    "    print(\"正在验证配置...\")\n",
    "    \n",
    "    # 必需的键\n",
    "    required_keys = [\n",
    "        'device', 'image_size', 'batch_size', 'learning_rate', 'epochs',\n",
    "        'data_root', 'images_dir', 'annotations_dir', 'mean', 'std'\n",
    "    ]\n",
    "    \n",
    "    for key in required_keys:\n",
    "        assert key in config, f\"缺少必需的配置键：{key}\"\n",
    "    \n",
    "    # 验证数据类型和范围\n",
    "    assert isinstance(config['image_size'], (tuple, list)) and len(config['image_size']) == 2, \\\n",
    "        \"image_size必须是包含2个整数的元组/列表\"\n",
    "    assert all(isinstance(x, int) and x > 0 for x in config['image_size']), \\\n",
    "        \"image_size的值必须是正整数\"\n",
    "    \n",
    "    assert isinstance(config['batch_size'], int) and config['batch_size'] > 0, \\\n",
    "        \"batch_size必须是正整数\"\n",
    "    assert config['batch_size'] <= 64, \\\n",
    "        \"batch_size > 64可能导致内存问题\"\n",
    "    \n",
    "    assert isinstance(config['learning_rate'], (int, float)) and config['learning_rate'] > 0, \\\n",
    "        \"learning_rate必须是正数\"\n",
    "    assert config['learning_rate'] <= 1.0, \\\n",
    "        \"learning_rate > 1.0异常高\"\n",
    "    \n",
    "    assert isinstance(config['epochs'], int) and config['epochs'] > 0, \\\n",
    "        \"epochs必须是正整数\"\n",
    "    \n",
    "    assert len(config['mean']) == 3 and len(config['std']) == 3, \\\n",
    "        \"mean和std必须包含RGB通道的3个值\"\n",
    "    assert all(0 <= x <= 1 for x in config['mean']), \\\n",
    "        \"mean值应该在0和1之间\"\n",
    "    assert all(x > 0 for x in config['std']), \\\n",
    "        \"std值必须为正数\"\n",
    "    \n",
    "    # 内存优化警告\n",
    "    if config['batch_size'] * config['image_size'][0] * config['image_size'][1] > 300 * 300 * 32:\n",
    "        print(\"检测到大批次大小 - 如果遇到内存问题请考虑减少\")\n",
    "    \n",
    "    if config['num_workers'] > 8:\n",
    "        print(\"检测到高num_workers - 如果遇到问题请考虑减少\")\n",
    "    \n",
    "    print(\"配置验证通过！\")\n",
    "    return True\n",
    "\n",
    "# 验证配置\n",
    "validate_config(CONFIG)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 300,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "配置摘要：\n",
      "==================================================\n",
      "\n",
      "设备和性能：\n",
      "  device: cpu\n",
      "  num_workers: 4\n",
      "  pin_memory: True\n",
      "\n",
      "图像处理：\n",
      "  image_size: (300, 300)\n",
      "  mean: [0.485, 0.456, 0.406]\n",
      "  std: [0.229, 0.224, 0.225]\n",
      "\n",
      "模型架构：\n",
      "  backbone: vgg16\n",
      "  pretrained: True\n",
      "  num_classes: None\n",
      "\n",
      "训练参数：\n",
      "  batch_size: 16\n",
      "  learning_rate: 0.001\n",
      "  epochs: 50\n",
      "\n",
      "评估指标：\n",
      "  confidence_threshold: 0.5\n",
      "  nms_threshold: 0.5\n",
      "  iou_threshold: 0.5\n"
     ]
    }
   ],
   "source": [
    "# 显示配置摘要\n",
    "def display_config_summary():\n",
    "    \"\"\"显示配置的格式化摘要\"\"\"\n",
    "    print(\"配置摘要：\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    sections = {\n",
    "        \"设备和性能\": ['device', 'num_workers', 'pin_memory'],\n",
    "        \"图像处理\": ['image_size', 'mean', 'std'],\n",
    "        \"模型架构\": ['backbone', 'pretrained', 'num_classes'],\n",
    "        \"训练参数\": ['batch_size', 'learning_rate', 'epochs'],\n",
    "        \"评估指标\": ['confidence_threshold', 'nms_threshold', 'iou_threshold']\n",
    "    }\n",
    "    \n",
    "    for section, keys in sections.items():\n",
    "        print(f\"\\n{section}:\")\n",
    "        for key in keys:\n",
    "            if key in CONFIG:\n",
    "                print(f\"  {key}: {CONFIG[key]}\")\n",
    "\n",
    "display_config_summary()"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 数据集下载和文件夹结构标准化\n",
    "\n",
    "### 数据集背景\n",
    "我们将使用来自Kaggle的**植物病害检测数据集**，该数据集包含：\n",
    "- **目标**：检测和分类各种植物病害\n",
    "- **格式**：带有XML标注的图像（PASCAL VOC格式）\n",
    "- **类别**：多种植物病害类别 + 健康植物\n",
    "- **用例**：农业监控和早期病害检测\n",
    "\n",
    "这个数据集非常适合学习目标检测，因为它具有：\n",
    "- 真实世界的农业图像\n",
    "- 不同大小和位置的目标\n",
    "- 可能存在不平衡的多个类别\n",
    "- 实际应用价值"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 301,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Kaggle API设置说明：\n",
      "==================================================\n",
      "1. 访问 https://www.kaggle.com/account\n",
      "2. 滚动到'API'部分\n",
      "3. 点击'Create New API Token'\n",
      "4. 下载kaggle.json文件\n",
      "5. 将其放置在：\n",
      "   - Windows: C:/Users/<USER>/.kaggle/kaggle.json\n",
      "   - Linux/Mac: ~/.kaggle/kaggle.json\n",
      "6. 设置权限：chmod 600 ~/.kaggle/kaggle.json (Linux/Mac)\n",
      "\n",
      "设置完成后，运行下一个单元格下载数据集！\n"
     ]
    }
   ],
   "source": [
    "# Kaggle API设置说明\n",
    "def setup_kaggle_api():\n",
    "    \"\"\"设置Kaggle API的说明\"\"\"\n",
    "    print(\"Kaggle API设置说明：\")\n",
    "    print(\"=\" * 50)\n",
    "    print(\"1. 访问 https://www.kaggle.com/account\")\n",
    "    print(\"2. 滚动到'API'部分\")\n",
    "    print(\"3. 点击'Create New API Token'\")\n",
    "    print(\"4. 下载kaggle.json文件\")\n",
    "    print(\"5. 将其放置在：\")\n",
    "    print(\"   - Windows: C:/Users/<USER>/.kaggle/kaggle.json\")\n",
    "    print(\"   - Linux/Mac: ~/.kaggle/kaggle.json\")\n",
    "    print(\"6. 设置权限：chmod 600 ~/.kaggle/kaggle.json (Linux/Mac)\")\n",
    "    print(\"\\n设置完成后，运行下一个单元格下载数据集！\")\n",
    "\n",
    "setup_kaggle_api()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 302,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "正在下载植物病害检测数据集...\n",
      "这可能需要几分钟时间，取决于您的网络连接。\n",
      "数据集下载到：C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\kamipakistan\\plant-diseases-detection-dataset\\versions\\2\n",
      "相对路径：..\\..\\.cache\\kagglehub\\datasets\\kamipakistan\\plant-diseases-detection-dataset\\versions\\2\n"
     ]
    }
   ],
   "source": [
    "# 数据集下载函数\n",
    "import kagglehub\n",
    "import zipfile\n",
    "import shutil\n",
    "from tqdm import tqdm\n",
    "\n",
    "def download_dataset():\n",
    "    \"\"\"下载并解压植物病害数据集\"\"\"\n",
    "    try:\n",
    "        print(\"正在下载植物病害检测数据集...\")\n",
    "        print(\"这可能需要几分钟时间，取决于您的网络连接。\")\n",
    "        \n",
    "        # 下载数据集的最新版本\n",
    "        absolute_path = kagglehub.dataset_download(\"kamipakistan/plant-diseases-detection-dataset\")\n",
    "        print(f\"数据集下载到：{absolute_path}\")\n",
    "        \n",
    "        # 转换为相对路径以提高可移植性\n",
    "        relative_path = os.path.relpath(absolute_path, os.getcwd())\n",
    "        print(f\"相对路径：{relative_path}\")\n",
    "        \n",
    "        return absolute_path\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"下载数据集时出错：{e}\")\n",
    "        print(\"请检查您的Kaggle API设置和网络连接。\")\n",
    "        print(\"\\n替代方案：您可以从以下地址手动下载数据集：\")\n",
    "        print(\"   https://www.kaggle.com/datasets/kamipakistan/plant-diseases-detection-dataset\")\n",
    "        print(\"   将其解压到项目目录中的'data'文件夹。\")\n",
    "        return None\n",
    "\n",
    "# 下载数据集\n",
    "dataset_path = download_dataset()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 303,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "正在组织数据集结构...\n",
      "找到5099个图像文件\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "复制图像: 100%|██████████| 5099/5099 [00:01<00:00, 4659.75it/s]\n"
     ]
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "找到0个标注文件\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "复制标注: 0it [00:00, ?it/s]"
     ]
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "数据集组织成功！\n",
      "图像：data\\images\n",
      "标注：data\\annotations\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "\n"
     ]
    }
   ],
   "source": [
    "# 目录结构组织\n",
    "import xml.etree.ElementTree as ET\n",
    "import glob\n",
    "\n",
    "def organize_dataset(source_path, target_path):\n",
    "    \"\"\"将数据集组织成标准结构\"\"\"\n",
    "    if source_path is None:\n",
    "        print(\"未提供源路径。请先下载数据集。\")\n",
    "        return False\n",
    "    \n",
    "    print(\"正在组织数据集结构...\")\n",
    "    \n",
    "    # 创建目标目录\n",
    "    target_path = Path(target_path)\n",
    "    images_dir = target_path / 'images'\n",
    "    annotations_dir = target_path / 'annotations'\n",
    "    \n",
    "    images_dir.mkdir(parents=True, exist_ok=True)\n",
    "    annotations_dir.mkdir(parents=True, exist_ok=True)\n",
    "    \n",
    "    # 在源目录中查找所有文件\n",
    "    source_path = Path(source_path)\n",
    "    \n",
    "    # 复制图像文件\n",
    "    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']\n",
    "    image_files = []\n",
    "    for ext in image_extensions:\n",
    "        image_files.extend(glob.glob(str(source_path / '**' / f'*{ext}'), recursive=True))\n",
    "    \n",
    "    print(f\"找到{len(image_files)}个图像文件\")\n",
    "    \n",
    "    for img_file in tqdm(image_files, desc=\"复制图像\"):\n",
    "        img_path = Path(img_file)\n",
    "        target_img = images_dir / img_path.name\n",
    "        if not target_img.exists():\n",
    "            shutil.copy2(img_file, target_img)\n",
    "    \n",
    "    # 复制标注文件\n",
    "    xml_files = glob.glob(str(source_path / '**' / '*.xml'), recursive=True)\n",
    "    print(f\"找到{len(xml_files)}个标注文件\")\n",
    "    \n",
    "    for xml_file in tqdm(xml_files, desc=\"复制标注\"):\n",
    "        xml_path = Path(xml_file)\n",
    "        target_xml = annotations_dir / xml_path.name\n",
    "        if not target_xml.exists():\n",
    "            shutil.copy2(xml_file, target_xml)\n",
    "    \n",
    "    print(f\"数据集组织成功！\")\n",
    "    print(f\"图像：{images_dir}\")\n",
    "    print(f\"标注：{annotations_dir}\")\n",
    "    \n",
    "    return True\n",
    "\n",
    "# 组织数据集\n",
    "if dataset_path:\n",
    "    success = organize_dataset(dataset_path, CONFIG['data_root'])\n",
    "    if success:\n",
    "        CONFIG['images_dir'] = str(Path(CONFIG['data_root']) / 'images')\n",
    "        CONFIG['annotations_dir'] = str(Path(CONFIG['data_root']) / 'annotations')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 304,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "数据集结构分析：\n",
      "==================================================\n",
      "总图像数：10198\n",
      "总标注数：50\n",
      "🔗 匹配的图像-标注对：50\n",
      "无标注的图像：5049\n",
      "\n",
      "样本标注分析 (Image100_1.xml)：\n",
      "   图像：Image100_1.jpg\n",
      "   尺寸：100x100\n",
      "   目标：3\n",
      "   样本中的类别：{'pest_damage', 'fungal_infection', 'bacterial_spot'}\n",
      "\n",
      "数据集适用于目标检测训练！\n"
     ]
    }
   ],
   "source": [
    "# 增强的数据集分析和验证\n",
    "def analyze_dataset_structure():\n",
    "    \"\"\"分析和验证数据集结构，进行全面检查\"\"\"\n",
    "    images_dir = Path(CONFIG['images_dir'])\n",
    "    annotations_dir = Path(CONFIG['annotations_dir'])\n",
    "    \n",
    "    print(\"数据集结构分析：\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    # 检查目录是否存在\n",
    "    if not images_dir.exists():\n",
    "        print(f\"未找到图像目录：{images_dir}\")\n",
    "        return False, \"missing_images_dir\"\n",
    "    \n",
    "    if not annotations_dir.exists():\n",
    "        print(f\"未找到标注目录：{annotations_dir}\")\n",
    "        return False, \"missing_annotations_dir\"\n",
    "    \n",
    "    # 统计文件数量\n",
    "    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']\n",
    "    image_files = []\n",
    "    for ext in image_extensions:\n",
    "        image_files.extend(list(images_dir.glob(ext)))\n",
    "        image_files.extend(list(images_dir.glob(ext.upper())))\n",
    "    \n",
    "    xml_files = list(annotations_dir.glob('*.xml'))\n",
    "    \n",
    "    print(f\"总图像数：{len(image_files)}\")\n",
    "    print(f\"总标注数：{len(xml_files)}\")\n",
    "    \n",
    "    # 验证最低要求\n",
    "    if len(image_files) == 0:\n",
    "        print(\"未找到图像文件！\")\n",
    "        return False, \"no_images\"\n",
    "    \n",
    "    if len(xml_files) == 0:\n",
    "        print(\"未找到标注文件！\")\n",
    "        print(\"\\n数据集类型检测：\")\n",
    "        print(\"   这似乎是一个没有边界框标注的图像分类数据集。\")\n",
    "        print(\"   对于使用SSD的目标检测，我们需要XML格式的边界框标注。\")\n",
    "        return False, \"no_annotations\"\n",
    "    \n",
    "    # 检查匹配对\n",
    "    image_names = {f.stem for f in image_files}\n",
    "    xml_names = {f.stem for f in xml_files}\n",
    "    \n",
    "    matched_pairs = len(image_names.intersection(xml_names))\n",
    "    print(f\"🔗 匹配的图像-标注对：{matched_pairs}\")\n",
    "    \n",
    "    if matched_pairs < len(image_files):\n",
    "        missing_annotations = image_names - xml_names\n",
    "        print(f\"无标注的图像：{len(missing_annotations)}\")\n",
    "        if len(missing_annotations) <= 5:\n",
    "            print(f\"   缺失：{list(missing_annotations)}\")\n",
    "    \n",
    "    if matched_pairs < len(xml_files):\n",
    "        missing_images = xml_names - image_names\n",
    "        print(f\"无图像的标注：{len(missing_images)}\")\n",
    "        if len(missing_images) <= 5:\n",
    "            print(f\"   缺失：{list(missing_images)}\")\n",
    "    \n",
    "    # 样本文件分析\n",
    "    if xml_files:\n",
    "        sample_xml = xml_files[0]\n",
    "        try:\n",
    "            tree = ET.parse(sample_xml)\n",
    "            root = tree.getroot()\n",
    "            \n",
    "            # 提取样本信息\n",
    "            filename = root.find('filename').text if root.find('filename') is not None else '未知'\n",
    "            size = root.find('size')\n",
    "            if size is not None:\n",
    "                width = size.find('width').text\n",
    "                height = size.find('height').text\n",
    "                print(f\"\\n样本标注分析 ({sample_xml.name})：\")\n",
    "                print(f\"   图像：{filename}\")\n",
    "                print(f\"   尺寸：{width}x{height}\")\n",
    "                \n",
    "                objects = root.findall('object')\n",
    "                print(f\"   目标：{len(objects)}\")\n",
    "                \n",
    "                if objects:\n",
    "                    classes = [obj.find('name').text for obj in objects if obj.find('name') is not None]\n",
    "                    print(f\"   样本中的类别：{set(classes)}\")\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"解析样本XML时出错：{e}\")\n",
    "    \n",
    "    # 确定数据集有效性\n",
    "    if matched_pairs > 0:\n",
    "        print(f\"\\n数据集适用于目标检测训练！\")\n",
    "        return True, \"valid\"\n",
    "    else:\n",
    "        print(f\"\\n数据集不适用于目标检测训练。\")\n",
    "        return False, \"invalid\"\n",
    "\n",
    "# 分析数据集\n",
    "dataset_valid, dataset_status = analyze_dataset_structure()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 数据可视化和分析\n",
    "\n",
    "理解我们的数据集对于设计有效的SSD模型至关重要。我们将分析：\n",
    "- 类别分布以识别不平衡\n",
    "- 边界框特征用于锚框设计\n",
    "- 空间分布模式\n",
    "- 带标注的样本可视化"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 309,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "正在解析50个标注文件...\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "解析标注: 100%|██████████| 50/50 [00:00<00:00, 3697.57it/s]"
     ]
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "从50张图像中解析了109个边界框\n",
      "找到6个唯一类别\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "\n"
     ]
    }
   ],
   "source": [
    "# 解析所有标注并提取信息\n",
    "import xml.etree.ElementTree as ET\n",
    "from collections import defaultdict, Counter\n",
    "import matplotlib.patches as patches\n",
    "\n",
    "def parse_all_annotations():\n",
    "    \"\"\"解析所有XML标注并提取边界框信息\"\"\"\n",
    "    annotations_dir = Path(CONFIG['annotations_dir'])\n",
    "    \n",
    "    if not annotations_dir.exists():\n",
    "        print(\"未找到标注目录！\")\n",
    "        return None\n",
    "    \n",
    "    xml_files = list(annotations_dir.glob('*.xml'))\n",
    "    \n",
    "    if len(xml_files) == 0:\n",
    "        print(\"未找到标注文件！\")\n",
    "        print(\"解决方案：\")\n",
    "        print(\"   1. 创建合成标注：synthetic_created = create_synthetic_annotations(50)\")\n",
    "        print(\"   2. 使用带有标注的不同数据集\")\n",
    "        print(\"   3. 使用LabelImg或类似工具手动标注图像\")\n",
    "        return None\n",
    "    \n",
    "    data = {\n",
    "        'filenames': [],\n",
    "        'image_widths': [],\n",
    "        'image_heights': [],\n",
    "        'classes': [],\n",
    "        'bbox_widths': [],\n",
    "        'bbox_heights': [],\n",
    "        'bbox_centers_x': [],\n",
    "        'bbox_centers_y': [],\n",
    "        'bbox_areas': [],\n",
    "        'objects_per_image': []\n",
    "    }\n",
    "    \n",
    "    print(f\"正在解析{len(xml_files)}个标注文件...\")\n",
    "    \n",
    "    for xml_file in tqdm(xml_files, desc=\"解析标注\"):\n",
    "        try:\n",
    "            tree = ET.parse(xml_file)\n",
    "            root = tree.getroot()\n",
    "            \n",
    "            # 图像信息\n",
    "            filename = root.find('filename')\n",
    "            filename = filename.text if filename is not None else xml_file.stem\n",
    "            \n",
    "            size = root.find('size')\n",
    "            if size is None:\n",
    "                continue\n",
    "                \n",
    "            img_width = int(size.find('width').text)\n",
    "            img_height = int(size.find('height').text)\n",
    "            \n",
    "            # 此图像中的目标\n",
    "            objects = root.findall('object')\n",
    "            objects_count = len(objects)\n",
    "            \n",
    "            for obj in objects:\n",
    "                # 类别名称\n",
    "                name = obj.find('name')\n",
    "                if name is None:\n",
    "                    continue\n",
    "                class_name = name.text\n",
    "                \n",
    "                # 边界框\n",
    "                bbox = obj.find('bndbox')\n",
    "                if bbox is None:\n",
    "                    continue\n",
    "                    \n",
    "                xmin = int(bbox.find('xmin').text)\n",
    "                ymin = int(bbox.find('ymin').text)\n",
    "                xmax = int(bbox.find('xmax').text)\n",
    "                ymax = int(bbox.find('ymax').text)\n",
    "                \n",
    "                # 计算边界框属性\n",
    "                bbox_width = xmax - xmin\n",
    "                bbox_height = ymax - ymin\n",
    "                bbox_area = bbox_width * bbox_height\n",
    "                center_x = (xmin + xmax) / 2\n",
    "                center_y = (ymin + ymax) / 2\n",
    "                \n",
    "                # 存储数据\n",
    "                data['filenames'].append(filename)\n",
    "                data['image_widths'].append(img_width)\n",
    "                data['image_heights'].append(img_height)\n",
    "                data['classes'].append(class_name)\n",
    "                data['bbox_widths'].append(bbox_width)\n",
    "                data['bbox_heights'].append(bbox_height)\n",
    "                data['bbox_centers_x'].append(center_x / img_width)  # 标准化\n",
    "                data['bbox_centers_y'].append(center_y / img_height)  # 标准化\n",
    "                data['bbox_areas'].append(bbox_area)\n",
    "            \n",
    "            # 存储每张图像的目标数量\n",
    "            if objects_count > 0:\n",
    "                data['objects_per_image'].extend([objects_count] * objects_count)\n",
    "                \n",
    "        except Exception as e:\n",
    "            print(f\"解析{xml_file}时出错：{e}\")\n",
    "            continue\n",
    "    \n",
    "    # 转换为DataFrame以便分析\n",
    "    df = pd.DataFrame(data)\n",
    "    \n",
    "    if len(df) == 0:\n",
    "        print(\"在XML文件中未找到有效标注！\")\n",
    "        print(\"这可能表明标注文件损坏或格式不正确。\")\n",
    "        return None\n",
    "    \n",
    "    print(f\"从{len(df['filenames'].unique())}张图像中解析了{len(df)}个边界框\")\n",
    "    print(f\"找到{len(df['classes'].unique())}个唯一类别\")\n",
    "    \n",
    "    return df\n",
    "\n",
    "# 解析标注\n",
    "annotations_df = parse_all_annotations()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 数据预处理和数据增强\n",
    "\n",
    "### 为什么选择Albumentations？\n",
    "对于目标检测，我们需要能够**同时变换图像和边界框**的数据增强。Albumentations是黄金标准，因为：\n",
    "- 自动处理边界框坐标变换\n",
    "- 快速且优化的实现\n",
    "- 广泛的增强选项\n",
    "- 与PyTorch轻松集成\n",
    "\n",
    "### SSD特定要求：\n",
    "- 固定输入尺寸（SSD300为300×300）\n",
    "- 适当的标准化\n",
    "- 边界框格式兼容性"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 313,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Albumentations和IPyWidgets导入成功！\n",
      "Albumentations版本：1.4.24\n"
     ]
    }
   ],
   "source": [
    "# 导入数据增强库\n",
    "import albumentations as A\n",
    "from albumentations.pytorch import ToTensorV2\n",
    "import ipywidgets as widgets\n",
    "from IPython.display import display, clear_output\n",
    "\n",
    "print(\"Albumentations和IPyWidgets导入成功！\")\n",
    "print(f\"Albumentations版本：{A.__version__}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 314,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "数据增强管道创建完成！\n",
      "训练变换：11步\n",
      "验证变换：3步\n"
     ]
    }
   ],
   "source": [
    "# 定义数据增强管道\n",
    "def get_training_transforms():\n",
    "    \"\"\"定义训练数据增强管道\"\"\"\n",
    "    return A.Compose([\n",
    "        # 空间变换\n",
    "        A.HorizontalFlip(p=0.5),\n",
    "        A.RandomRotate90(p=0.2),\n",
    "        A.ShiftScaleRotate(\n",
    "            shift_limit=0.1, \n",
    "            scale_limit=0.2, \n",
    "            rotate_limit=15, \n",
    "            p=0.5\n",
    "        ),\n",
    "        \n",
    "        # 光度变换\n",
    "        A.RandomBrightnessContrast(\n",
    "            brightness_limit=CONFIG['brightness_limit'],\n",
    "            contrast_limit=CONFIG['contrast_limit'],\n",
    "            p=0.5\n",
    "        ),\n",
    "        A.HueSaturationValue(\n",
    "            hue_shift_limit=10,\n",
    "            sat_shift_limit=20,\n",
    "            val_shift_limit=10,\n",
    "            p=0.3\n",
    "        ),\n",
    "        A.RandomGamma(gamma_limit=(80, 120), p=0.3),\n",
    "        \n",
    "        # 噪声和模糊\n",
    "        A.OneOf([\n",
    "            A.GaussNoise(var_limit=(10, 50), p=0.3),\n",
    "            A.GaussianBlur(blur_limit=3, p=0.3),\n",
    "            A.MotionBlur(blur_limit=3, p=0.3),\n",
    "        ], p=0.2),\n",
    "        \n",
    "        # 最终调整\n",
    "        A.Resize(CONFIG['image_size'][0], CONFIG['image_size'][1]),\n",
    "        A.Normalize(\n",
    "            mean=CONFIG['mean'],\n",
    "            std=CONFIG['std'],\n",
    "            max_pixel_value=255.0\n",
    "        ),\n",
    "        ToTensorV2()\n",
    "    ], bbox_params=A.BboxParams(\n",
    "        format='pascal_voc',\n",
    "        label_fields=['class_labels'],\n",
    "        min_visibility=0.3\n",
    "    ))\n",
    "\n",
    "def get_validation_transforms():\n",
    "    \"\"\"定义验证数据变换管道\"\"\"\n",
    "    return A.Compose([\n",
    "        A.Resize(CONFIG['image_size'][0], CONFIG['image_size'][1]),\n",
    "        A.Normalize(\n",
    "            mean=CONFIG['mean'],\n",
    "            std=CONFIG['std'],\n",
    "            max_pixel_value=255.0\n",
    "        ),\n",
    "        ToTensorV2()\n",
    "    ], bbox_params=A.BboxParams(\n",
    "        format='pascal_voc',\n",
    "        label_fields=['class_labels'],\n",
    "        min_visibility=0.3\n",
    "    ))\n",
    "\n",
    "# 创建变换管道\n",
    "train_transforms = get_training_transforms()\n",
    "val_transforms = get_validation_transforms()\n",
    "\n",
    "print(\"数据增强管道创建完成！\")\n",
    "print(f\"训练变换：{len(train_transforms.transforms)}步\")\n",
    "print(f\"验证变换：{len(val_transforms.transforms)}步\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "---\n",
    "\n",
    "# 任务5：SSD模型构建\n",
    "\n",
    "## 概述\n",
    "\n",
    "在本节中，我们将为植物病害检测构建完整的SSD（Single Shot MultiBox Detector）模型。我们将涵盖：\n",
    "\n",
    "### 学习目标：\n",
    "- **迁移学习**：利用预训练骨干网络（VGG16、ResNet50、MobileNet）\n",
    "- **SSD架构**：理解多尺度特征提取和检测头\n",
    "- **锚框生成**：学习先验框如何实现单次检测\n",
    "- **特征可视化**：查看不同层如何捕获不同语义级别\n",
    "- **模型变体**：比较不同骨干架构\n",
    "\n",
    "### 🏛️ SSD架构组件：\n",
    "1. **骨干网络**：特征提取（VGG16/ResNet/MobileNet）\n",
    "2. **额外特征层**：用于多尺度检测的附加卷积层\n",
    "3. **锚框生成**：多尺度和纵横比的先验框\n",
    "4. **检测头**：分类和定位预测层\n",
    "5. **特征金字塔**：用于检测不同大小目标的多尺度特征图\n",
    "\n",
    "让我们开始构建！🚀"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 324,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "SSD模型构建库导入成功！\n",
      "PyTorch版本：2.5.1+cpu\n",
      "设备：cpu\n"
     ]
    }
   ],
   "source": [
    "# SSD模型构建 - 导入所需库\n",
    "import torch\n",
    "import torch.nn as nn\n",
    "import torch.nn.functional as F\n",
    "import torchvision.models as models\n",
    "from torchvision.models.detection import ssd300_vgg16\n",
    "from torchvision.models.detection.ssd import SSDHead\n",
    "from torchvision.models.detection.anchor_utils import AnchorGenerator\n",
    "import math\n",
    "from collections import OrderedDict\n",
    "from typing import List, Dict, Tuple, Optional\n",
    "\n",
    "print(\"SSD模型构建库导入成功！\")\n",
    "print(f\"PyTorch版本：{torch.__version__}\")\n",
    "print(f\"设备：{CONFIG['device']}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "---\n",
    "\n",
    "# 任务6：训练过程和监控\n",
    "\n",
    "## 概述\n",
    "\n",
    "训练SSD模型需要仔细关注损失函数、优化策略和监控。本节涵盖：\n",
    "\n",
    "### 学习目标：\n",
    "- **MultiBox损失函数**：理解定位+分类损失\n",
    "- **困难负样本挖掘**：处理目标检测中的极端类别不平衡\n",
    "- **学习率调度**：动态学习率策略\n",
    "- **训练监控**：TensorBoard集成和进度跟踪\n",
    "- **验证策略**：训练期间的适当评估\n",
    "\n",
    "### 关键组件：\n",
    "1. **MultiBox损失**：Smooth L1（定位）+ 交叉熵（分类）\n",
    "2. **困难负样本挖掘**：解决1000:1的负正样本比例\n",
    "3. **学习率调度器**：CosineAnnealingLR实现更好收敛\n",
    "4. **进度监控**：使用tqdm的实时训练指标\n",
    "5. **模型检查点**：保存最佳模型并恢复训练\n",
    "\n",
    "让我们构建专业的训练管道！🏗️"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 332,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "训练库导入成功！\n",
      "PyTorch优化器：可用\n",
      "TensorBoard：可用\n",
      "进度条：tqdm就绪\n"
     ]
    }
   ],
   "source": [
    "# 训练过程和监控 - 导入所需库\n",
    "import torch.optim as optim\n",
    "from torch.optim.lr_scheduler import CosineAnnealingLR, StepLR, ReduceLROnPlateau\n",
    "from torch.utils.tensorboard import SummaryWriter\n",
    "from tqdm.auto import tqdm\n",
    "import time\n",
    "import copy\n",
    "from datetime import datetime\n",
    "\n",
    "print(\"训练库导入成功！\")\n",
    "print(\"PyTorch优化器：可用\")\n",
    "print(\"TensorBoard：可用\")\n",
    "print(\"进度条：tqdm就绪\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "---\n",
    "\n",
    "# 任务7：模型评估和可视化\n",
    "\n",
    "## 概述\n",
    "\n",
    "评估目标检测模型需要专门的指标和可视化技术。本节涵盖：\n",
    "\n",
    "### 学习目标：\n",
    "- **mAP计算**：理解平均精度均值\n",
    "- **IoU阈值**：不同IoU下的性能评估\n",
    "- **混淆矩阵**：类别级别的性能分析\n",
    "- **检测可视化**：边界框和置信度显示\n",
    "- **错误分析**：识别模型弱点\n",
    "\n",
    "### 关键指标：\n",
    "1. **mAP@0.5**：IoU=0.5时的平均精度均值\n",
    "2. **mAP@0.5:0.95**：多个IoU阈值的平均mAP\n",
    "3. **精确率/召回率曲线**：每个类别的性能\n",
    "4. **置信度分布**：预测质量分析\n",
    "5. **检测示例**：成功和失败案例\n",
    "\n",
    "让我们全面评估模型性能！📊"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "---\n",
    "\n",
    "# 任务8：推理和部署\n",
    "\n",
    "## 概述\n",
    "\n",
    "将训练好的SSD模型部署到生产环境需要优化和实用工具。本节涵盖：\n",
    "\n",
    "### 学习目标：\n",
    "- **模型优化**：ONNX导出和量化\n",
    "- **推理管道**：高效的预测流程\n",
    "- **Web界面**：Gradio/Streamlit部署\n",
    "- **批量处理**：大规模图像处理\n",
    "- **性能监控**：推理时间和准确性\n",
    "\n",
    "### 部署选项：\n",
    "1. **ONNX导出**：跨平台兼容性\n",
    "2. **TensorRT优化**：GPU加速推理\n",
    "3. **Docker容器化**：可移植部署\n",
    "4. **REST API**：Web服务集成\n",
    "5. **移动端部署**：边缘设备优化\n",
    "\n",
    "让我们将模型投入实际应用！🚀"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.9"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
